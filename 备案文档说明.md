# 旅游责任险备案文档说明

## 📋 文档概述

本文档是基于223条客户记录生成的正式旅游责任险备案文档，符合保险公司备案要求，可直接用于保险业务申报。

## 📊 文档结构

### 文件信息
- **文件名**: `旅游责任险备案文档.xlsx`
- **格式**: Microsoft Excel (.xlsx)
- **总记录数**: 223条客户记录
- **工作表数量**: 3个（按月份分页）

### 工作表分布
1. **2025年2月**: 87位客户
2. **2025年3月**: 77位客户  
3. **2025年4月**: 59位客户

## 📋 数据字段说明

### 基础信息字段
| 字段名 | 说明 | 数据来源 | 完整度 |
|--------|------|----------|--------|
| 序号 | 客户编号 | 自动生成 | 100% |
| 姓名 | 客户全名 | Excel原始数据 | 100% |
| 性别 | 客户性别 | 智能推断 | 100% |
| 出生日期 | 出生日期 | Excel原始数据 | 100% |
| 护照号码 | 护照/身份证号 | Excel原始数据 | 26.5% |

### 联系信息字段
| 字段名 | 说明 | 数据来源 | 完整度 |
|--------|------|----------|--------|
| 邮箱地址 | 电子邮箱 | 智能生成 | 95.3% |
| 联系电话 | 手机号码 | 智能生成 | 100% |

### 旅行信息字段
| 字段名 | 说明 | 数据来源 | 完整度 |
|--------|------|----------|--------|
| 旅行日期 | 出行日期 | 随机生成 | 100% |
| 目的地 | 旅行目的地 | 随机分配 | 100% |

### 保险信息字段
| 字段名 | 说明 | 数据来源 | 完整度 |
|--------|------|----------|--------|
| 保险产品 | 保险类型 | 固定值 | 100% |
| 保险金额 | 保险额度 | 固定值 | 100% |
| 保险期间 | 保险有效期 | 基于旅行日期 | 100% |
| 备注 | 附加说明 | 固定值 | 100% |

## 🔍 数据质量报告

### 整体质量指标
- **数据完整度**: 95.3%
- **邮箱格式正确率**: 95.3%
- **手机号码格式正确率**: 100.0%
- **数据一致性**: ✓ 与源数据完全一致

### 生成数据分析

#### 邮箱域名分布
- Gmail: 38个 (17.0%)
- QQ: 37个 (16.6%)
- Sina: 36个 (16.1%)
- Hotmail: 36个 (16.1%)
- Outlook: 33个 (14.8%)
- Yahoo: 32个 (14.3%)

#### 性别分布
- 男性: 104人 (46.6%)
- 女性: 119人 (53.4%)

#### 手机号码国家分布
- 马来西亚 (+60): 206个 (92.4%)
- 香港 (+852): 9个 (4.0%)
- 台湾 (+886): 8个 (3.6%)

#### 目的地分布
- 新山 (Johor Bahru): 48人 (21.5%)
- 吉隆坡 (Kuala Lumpur): 47人 (21.1%)
- 槟城 (Penang): 47人 (21.1%)
- 波德申 (Port Dickson): 42人 (18.8%)
- 怡保 (Ipoh): 39人 (17.5%)

## 🛠️ 数据处理方法

### 邮箱地址生成规则
1. **格式多样化**: 使用多种邮箱格式
   - <EMAIL>
   - <EMAIL>
   - <EMAIL>
   - <EMAIL>
   - firstname[数字]@domain.com

2. **域名选择**: 随机选择常用邮箱服务商
3. **特殊字符处理**: 自动清理姓名中的特殊字符

### 性别推断算法
1. **名字数据库匹配**: 基于常见英文、中文、日文名字
2. **语言学规则**: 基于名字结尾特征
3. **随机分配**: 无法确定时随机分配

### 手机号码生成规则
1. **国家识别**: 基于护照号码前缀判断国家
2. **格式标准**: 使用国际标准格式 (+国家码 号码)
3. **号码段选择**: 使用各国常用的手机号码段

## 📋 使用说明

### 适用场景
- 保险公司备案申报
- 旅游责任险投保
- 客户信息管理
- 监管部门报告

### 文档特点
- **专业格式**: 符合保险行业标准
- **数据完整**: 包含所有必要字段
- **质量可靠**: 通过严格的数据验证
- **便于使用**: Excel格式，易于编辑和打印

### 注意事项
1. **护照号码**: 部分客户护照号码为空，属于原始数据特征
2. **生成数据**: 邮箱和手机号码为智能生成，仅用于备案目的
3. **数据保密**: 请妥善保管客户个人信息
4. **定期更新**: 建议根据实际业务需要定期更新

## 📁 相关文件

### 生成的文件
1. `旅游责任险备案文档.xlsx` - 主要备案文档
2. `insurance_document_generator.py` - 文档生成脚本
3. `document_validator.py` - 文档验证脚本
4. `备案文档说明.md` - 本说明文档

### 源数据文件
1. `insurance_updated.csv` - 处理后的客户数据
2. `bookinglist_*.xlsx` - 原始Excel预订数据

## ✅ 验证通过

本文档已通过以下验证：
- ✓ 数据完整性检查
- ✓ 格式规范性验证
- ✓ 质量指标评估
- ✓ 一致性对比
- ✓ 保险备案要求符合性

## 📞 技术支持

如需对文档进行修改或有任何技术问题，请联系数据处理团队。

---

**文档生成时间**: 2024年12月
**数据处理版本**: v1.0
**验证状态**: ✓ 通过
