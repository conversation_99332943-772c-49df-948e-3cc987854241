#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@file insurance_document_generator.py - 旅游责任险备案文档生成器
@function 基于客户数据生成正式的保险备案文档
"""

import pandas as pd
import random
import re
from datetime import datetime, timedelta
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows

class InsuranceDocumentGenerator:
    """
    @class InsuranceDocumentGenerator - 保险文档生成器类
    """
    
    def __init__(self):
        """
        @function __init__ - 初始化生成器
        """
        self.csv_file = r"C:\Users\<USER>\Downloads\new\insurance_updated.csv"
        self.output_file = r"C:\Users\<USER>\Downloads\new\旅游责任险备案文档.xlsx"
        
        # 邮箱域名列表
        self.email_domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'qq.com', 'sina.com']
        
        # 常见名字性别映射
        self.gender_names = {
            'male': ['john', 'david', 'michael', 'james', 'robert', 'william', 'richard', 'thomas', 'christopher', 'daniel',
                    'matthew', 'anthony', 'mark', 'donald', 'steven', 'paul', 'andrew', 'joshua', 'kenneth', 'kevin',
                    'brian', 'george', 'edward', 'ronald', 'timothy', 'jason', 'jeffrey', 'ryan', 'jacob', 'gary',
                    'nicholas', 'eric', 'jonathan', 'stephen', 'larry', 'justin', 'scott', 'brandon', 'benjamin', 'samuel',
                    'frank', 'raymond', 'alexander', 'patrick', 'jack', 'dennis', 'jerry', 'tyler', 'aaron', 'jose',
                    'henry', 'adam', 'douglas', 'nathan', 'peter', 'zachary', 'kyle', 'noah', 'alan', 'ethan',
                    'wei', 'ming', 'jun', 'hao', 'bin', 'kai', 'chen', 'yang', 'lei', 'feng', 'gang', 'qiang',
                    'takeshi', 'hiroshi', 'satoshi', 'kenji', 'yuki', 'daiki', 'ryota', 'shota', 'kenta', 'yuta'],
            'female': ['mary', 'patricia', 'jennifer', 'linda', 'elizabeth', 'barbara', 'susan', 'jessica', 'sarah', 'karen',
                      'nancy', 'lisa', 'betty', 'helen', 'sandra', 'donna', 'carol', 'ruth', 'sharon', 'michelle',
                      'laura', 'sarah', 'kimberly', 'deborah', 'dorothy', 'lisa', 'nancy', 'karen', 'betty', 'helen',
                      'sandra', 'donna', 'carol', 'ruth', 'sharon', 'michelle', 'laura', 'sarah', 'kimberly', 'deborah',
                      'amy', 'angela', 'brenda', 'emma', 'olivia', 'cynthia', 'marie', 'janet', 'catherine', 'frances',
                      'christine', 'samantha', 'debra', 'rachel', 'carolyn', 'janet', 'virginia', 'maria', 'heather', 'diane',
                      'mei', 'li', 'yan', 'xin', 'yu', 'jing', 'ling', 'fang', 'hui', 'qing', 'xia', 'min',
                      'yuki', 'akiko', 'keiko', 'yoko', 'michiko', 'sachiko', 'kumiko', 'mayumi', 'emi', 'mari']
        }
    
    def clean_name_for_email(self, name):
        """
        @function clean_name_for_email - 清理姓名用于生成邮箱
        @param {str} name - 原始姓名
        @returns {str} 清理后的姓名
        """
        if not name:
            return ""
        
        # 移除特殊字符，保留字母和空格
        cleaned = re.sub(r'[^a-zA-Z\s]', '', str(name))
        # 转换为小写
        cleaned = cleaned.lower().strip()
        # 移除多余空格
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        return cleaned
    
    def generate_email(self, name):
        """
        @function generate_email - 生成邮箱地址
        @param {str} name - 姓名
        @returns {str} 生成的邮箱地址
        """
        if not name:
            return ""
        
        cleaned_name = self.clean_name_for_email(name)
        if not cleaned_name:
            return ""
        
        # 分割姓名
        name_parts = cleaned_name.split()
        
        if len(name_parts) >= 2:
            # 使用名.姓格式
            first_name = name_parts[0]
            last_name = name_parts[-1]
            
            # 随机选择格式
            formats = [
                f"{first_name}.{last_name}",
                f"{first_name}{last_name}",
                f"{first_name}_{last_name}",
                f"{last_name}.{first_name}",
                f"{first_name}{random.randint(10, 99)}"
            ]
            
            email_prefix = random.choice(formats)
        else:
            # 只有一个名字
            email_prefix = f"{cleaned_name}{random.randint(10, 999)}"
        
        # 随机选择域名
        domain = random.choice(self.email_domains)
        
        return f"{email_prefix}@{domain}"
    
    def infer_gender(self, name):
        """
        @function infer_gender - 推断性别
        @param {str} name - 姓名
        @returns {str} 推断的性别
        """
        if not name:
            return "Unknown"
        
        name_lower = str(name).lower()
        
        # 检查是否包含常见的男性名字
        for male_name in self.gender_names['male']:
            if male_name in name_lower:
                return "Male"
        
        # 检查是否包含常见的女性名字
        for female_name in self.gender_names['female']:
            if female_name in name_lower:
                return "Female"
        
        # 基于名字结尾的简单规则
        if name_lower.endswith(('a', 'ia', 'ina', 'ana', 'ella', 'ette')):
            return "Female"
        elif name_lower.endswith(('son', 'er', 'o', 'us')):
            return "Male"
        
        # 随机分配
        return random.choice(["Male", "Female"])
    
    def generate_phone_number(self, passport_number=""):
        """
        @function generate_phone_number - 生成手机号码
        @param {str} passport_number - 护照号码
        @returns {str} 生成的手机号码
        """
        # 基于护照号码前缀判断可能的国家
        if not passport_number:
            return ""
        
        passport_str = str(passport_number).upper()
        
        # 根据护照格式生成对应国家的手机号码
        if passport_str.startswith(('K', 'H')):  # 香港
            return f"+852 {random.randint(6000, 9999)}{random.randint(1000, 9999)}"
        elif passport_str.startswith(('P', 'M')):  # 可能是菲律宾或马来西亚
            return f"+60 {random.randint(10, 19)}{random.randint(1000000, 9999999)}"
        elif passport_str.startswith(('3', '4', '5', '6')):  # 台湾
            return f"+886 {random.randint(*********, *********)}"
        elif passport_str.startswith('T'):  # 日本
            return f"+81 {random.randint(70, 90)}{random.randint(10000000, 99999999)}"
        else:
            # 默认生成马来西亚号码
            return f"+60 {random.randint(10, 19)}{random.randint(1000000, 9999999)}"
    
    def load_and_enhance_data(self):
        """
        @function load_and_enhance_data - 加载并增强数据
        @returns {DataFrame} 增强后的数据
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(self.csv_file)
            
            print(f"读取到 {len(df)} 条客户记录")
            
            # 生成邮箱地址
            print("正在生成邮箱地址...")
            df['email'] = df['Name'].apply(self.generate_email)
            
            # 推断性别
            print("正在推断性别...")
            df['gendre'] = df['Name'].apply(self.infer_gender)
            
            # 生成手机号码
            print("正在生成手机号码...")
            df['contact'] = df['passport'].apply(self.generate_phone_number)
            
            # 添加保险相关字段
            df['保险产品'] = '旅游责任险'
            df['保险金额'] = '100,000 MYR'
            df['保险期间'] = df['Travel Date'].apply(lambda x: f"{x} - {x}")
            df['备注'] = '马来西亚旅游'
            
            print("数据增强完成")
            return df
            
        except Exception as e:
            print(f"数据加载和增强过程中出错: {str(e)}")
            return None
    
    def group_by_month(self, df):
        """
        @function group_by_month - 按月份分组数据
        @param {DataFrame} df - 数据框
        @returns {dict} 按月份分组的数据字典
        """
        month_groups = {}
        
        for index, row in df.iterrows():
            travel_date = str(row['Travel Date'])
            
            if 'February' in travel_date:
                month = '2025年2月'
            elif 'March' in travel_date:
                month = '2025年3月'
            elif 'April' in travel_date:
                month = '2025年4月'
            else:
                month = '其他月份'
            
            if month not in month_groups:
                month_groups[month] = []
            
            month_groups[month].append(row)
        
        # 转换为DataFrame
        for month in month_groups:
            month_groups[month] = pd.DataFrame(month_groups[month])
        
        return month_groups
    
    def create_excel_document(self, month_groups):
        """
        @function create_excel_document - 创建Excel文档
        @param {dict} month_groups - 按月份分组的数据
        @returns {bool} 是否成功创建
        """
        try:
            # 创建工作簿
            wb = openpyxl.Workbook()
            
            # 删除默认工作表
            wb.remove(wb.active)
            
            # 定义列标题
            columns = [
                '序号', '姓名', '性别', '出生日期', '护照号码', 
                '邮箱地址', '联系电话', '旅行日期', '目的地',
                '保险产品', '保险金额', '保险期间', '备注'
            ]
            
            # 为每个月创建工作表
            for month, data in month_groups.items():
                if data.empty:
                    continue
                
                # 创建工作表
                ws = wb.create_sheet(title=month)
                
                # 添加标题
                title = f"{month}出行客户旅游责任险备案表"
                ws.merge_cells('A1:M1')
                ws['A1'] = title
                ws['A1'].font = Font(size=16, bold=True)
                ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
                
                # 添加统计信息
                ws.merge_cells('A2:M2')
                ws['A2'] = f"客户总数：{len(data)} 人"
                ws['A2'].font = Font(size=12)
                ws['A2'].alignment = Alignment(horizontal='center')
                
                # 添加列标题
                for col_idx, col_name in enumerate(columns, 1):
                    cell = ws.cell(row=4, column=col_idx, value=col_name)
                    cell.font = Font(bold=True)
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                
                # 添加数据
                for row_idx, (_, row) in enumerate(data.iterrows(), 5):
                    ws.cell(row=row_idx, column=1, value=row_idx-4)  # 序号
                    ws.cell(row=row_idx, column=2, value=row['Name'])
                    ws.cell(row=row_idx, column=3, value=row['gendre'])
                    ws.cell(row=row_idx, column=4, value=row['Date of birth'])
                    ws.cell(row=row_idx, column=5, value=row['passport'])
                    ws.cell(row=row_idx, column=6, value=row['email'])
                    ws.cell(row=row_idx, column=7, value=row['contact'])
                    ws.cell(row=row_idx, column=8, value=row['Travel Date'])
                    ws.cell(row=row_idx, column=9, value=row['Destination'])
                    ws.cell(row=row_idx, column=10, value=row['保险产品'])
                    ws.cell(row=row_idx, column=11, value=row['保险金额'])
                    ws.cell(row=row_idx, column=12, value=row['保险期间'])
                    ws.cell(row=row_idx, column=13, value=row['备注'])
                
                # 设置列宽
                column_widths = [6, 20, 8, 12, 15, 25, 18, 15, 15, 12, 12, 20, 12]
                for col_idx, width in enumerate(column_widths, 1):
                    ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = width
                
                # 添加边框
                thin_border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                
                for row in ws.iter_rows(min_row=4, max_row=len(data)+4, min_col=1, max_col=len(columns)):
                    for cell in row:
                        cell.border = thin_border
                        cell.alignment = Alignment(horizontal='center', vertical='center')
            
            # 保存文件
            wb.save(self.output_file)
            print(f"Excel文档已保存到: {self.output_file}")
            
            return True
            
        except Exception as e:
            print(f"创建Excel文档时出错: {str(e)}")
            return False
    
    def generate_document(self):
        """
        @function generate_document - 生成完整的备案文档
        @returns {bool} 是否成功生成
        """
        print("开始生成旅游责任险备案文档...")
        print("=" * 60)
        
        # 1. 加载和增强数据
        df = self.load_and_enhance_data()
        if df is None:
            return False
        
        # 2. 按月份分组
        print("正在按月份分组数据...")
        month_groups = self.group_by_month(df)
        
        for month, data in month_groups.items():
            print(f"{month}: {len(data)} 人")
        
        # 3. 创建Excel文档
        print("\n正在创建Excel文档...")
        success = self.create_excel_document(month_groups)
        
        if success:
            print("\n" + "=" * 60)
            print("旅游责任险备案文档生成完成!")
            print(f"文件位置: {self.output_file}")
            print("=" * 60)
        
        return success

def main():
    """
    @function main - 主函数
    @returns {None}
    """
    generator = InsuranceDocumentGenerator()
    generator.generate_document()

if __name__ == "__main__":
    main()
