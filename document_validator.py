#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@file document_validator.py - 备案文档验证器
@function 验证生成的旅游责任险备案文档的质量和完整性
"""

import pandas as pd
import openpyxl
import re
from collections import Counter

class DocumentValidator:
    """
    @class DocumentValidator - 文档验证器类
    """
    
    def __init__(self):
        """
        @function __init__ - 初始化验证器
        """
        self.excel_file = r"C:\Users\<USER>\Downloads\new\旅游责任险备案文档.xlsx"
        self.csv_file = r"C:\Users\<USER>\Downloads\new\insurance_updated.csv"
    
    def validate_email_format(self, email):
        """
        @function validate_email_format - 验证邮箱格式
        @param {str} email - 邮箱地址
        @returns {bool} 是否为有效格式
        """
        if not email:
            return False
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    def validate_phone_format(self, phone):
        """
        @function validate_phone_format - 验证手机号码格式
        @param {str} phone - 手机号码
        @returns {bool} 是否为有效格式
        """
        if not phone:
            return True  # 允许为空
        
        # 检查是否包含国际区号格式
        pattern = r'^\+\d{1,3}\s\d+'
        return bool(re.match(pattern, phone))
    
    def validate_excel_document(self):
        """
        @function validate_excel_document - 验证Excel文档
        @returns {dict} 验证结果
        """
        try:
            wb = openpyxl.load_workbook(self.excel_file)
            results = {
                'worksheets': [],
                'total_records': 0,
                'data_quality': {},
                'errors': []
            }
            
            print("=" * 80)
            print("旅游责任险备案文档验证报告")
            print("=" * 80)
            
            # 验证每个工作表
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                
                print(f"\n验证工作表: {sheet_name}")
                print("-" * 60)
                
                # 读取数据（跳过标题行）
                data = []
                for row in ws.iter_rows(min_row=5, values_only=True):
                    if row[0] is not None:  # 如果序号不为空
                        data.append(row)
                
                sheet_results = {
                    'name': sheet_name,
                    'record_count': len(data),
                    'data_quality': {}
                }
                
                print(f"记录数量: {len(data)}")
                
                if data:
                    # 验证数据质量
                    valid_emails = 0
                    valid_phones = 0
                    complete_records = 0
                    
                    for row in data:
                        # 检查完整性（姓名、性别、出生日期、邮箱）
                        if row[1] and row[2] and row[3] and row[5]:  # 姓名、性别、出生日期、邮箱
                            complete_records += 1
                        
                        # 验证邮箱格式
                        if self.validate_email_format(row[5]):
                            valid_emails += 1
                        
                        # 验证手机号码格式
                        if self.validate_phone_format(row[6]):
                            valid_phones += 1
                    
                    # 计算质量指标
                    email_quality = (valid_emails / len(data)) * 100
                    phone_quality = (valid_phones / len(data)) * 100
                    completeness = (complete_records / len(data)) * 100
                    
                    sheet_results['data_quality'] = {
                        'email_quality': email_quality,
                        'phone_quality': phone_quality,
                        'completeness': completeness
                    }
                    
                    print(f"数据完整度: {completeness:.1f}%")
                    print(f"邮箱格式正确率: {email_quality:.1f}%")
                    print(f"手机号码格式正确率: {phone_quality:.1f}%")
                
                results['worksheets'].append(sheet_results)
                results['total_records'] += len(data)
            
            return results
            
        except Exception as e:
            print(f"验证Excel文档时出错: {str(e)}")
            return None
    
    def compare_with_source(self):
        """
        @function compare_with_source - 与源CSV文件对比
        @returns {dict} 对比结果
        """
        try:
            # 读取原始CSV文件
            df_csv = pd.read_csv(self.csv_file)
            
            # 读取Excel文件的所有数据
            wb = openpyxl.load_workbook(self.excel_file)
            excel_records = 0
            
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                for row in ws.iter_rows(min_row=5, values_only=True):
                    if row[0] is not None:
                        excel_records += 1
            
            print(f"\n数据一致性检查:")
            print("-" * 60)
            print(f"CSV源文件记录数: {len(df_csv)}")
            print(f"Excel文档记录数: {excel_records}")
            print(f"数据一致性: {'✓ 一致' if len(df_csv) == excel_records else '✗ 不一致'}")
            
            return {
                'csv_records': len(df_csv),
                'excel_records': excel_records,
                'consistent': len(df_csv) == excel_records
            }
            
        except Exception as e:
            print(f"对比源文件时出错: {str(e)}")
            return None
    
    def analyze_generated_data(self):
        """
        @function analyze_generated_data - 分析生成的数据质量
        @returns {None}
        """
        try:
            wb = openpyxl.load_workbook(self.excel_file)
            
            all_emails = []
            all_genders = []
            all_phones = []
            
            # 收集所有数据
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                for row in ws.iter_rows(min_row=5, values_only=True):
                    if row[0] is not None:
                        all_emails.append(row[5])
                        all_genders.append(row[2])
                        all_phones.append(row[6])
            
            print(f"\n生成数据分析:")
            print("-" * 60)
            
            # 邮箱域名分析
            email_domains = []
            for email in all_emails:
                if email and '@' in str(email):
                    domain = str(email).split('@')[1]
                    email_domains.append(domain)
            
            domain_counts = Counter(email_domains)
            print("邮箱域名分布:")
            for domain, count in domain_counts.most_common():
                print(f"  {domain}: {count} 个")
            
            # 性别分布
            gender_counts = Counter(all_genders)
            print(f"\n性别分布:")
            for gender, count in gender_counts.items():
                print(f"  {gender}: {count} 人")
            
            # 手机号码国家分析
            phone_countries = []
            for phone in all_phones:
                if phone and phone.startswith('+'):
                    if '+852' in str(phone):
                        phone_countries.append('香港')
                    elif '+886' in str(phone):
                        phone_countries.append('台湾')
                    elif '+60' in str(phone):
                        phone_countries.append('马来西亚')
                    elif '+81' in str(phone):
                        phone_countries.append('日本')
                    else:
                        phone_countries.append('其他')
            
            country_counts = Counter(phone_countries)
            print(f"\n手机号码国家分布:")
            for country, count in country_counts.items():
                print(f"  {country}: {count} 个")
            
        except Exception as e:
            print(f"分析生成数据时出错: {str(e)}")
    
    def validate_document(self):
        """
        @function validate_document - 执行完整的文档验证
        @returns {bool} 验证是否通过
        """
        print("开始验证旅游责任险备案文档...")
        
        # 1. 验证Excel文档结构和质量
        excel_results = self.validate_excel_document()
        
        if not excel_results:
            return False
        
        # 2. 与源文件对比
        comparison_results = self.compare_with_source()
        
        # 3. 分析生成的数据
        self.analyze_generated_data()
        
        # 4. 总结验证结果
        print(f"\n" + "=" * 80)
        print("验证总结")
        print("=" * 80)
        
        total_records = excel_results['total_records']
        worksheets_count = len(excel_results['worksheets'])
        
        print(f"工作表数量: {worksheets_count}")
        print(f"总记录数: {total_records}")
        
        # 计算平均质量指标
        avg_completeness = sum([ws['data_quality'].get('completeness', 0) for ws in excel_results['worksheets']]) / worksheets_count
        avg_email_quality = sum([ws['data_quality'].get('email_quality', 0) for ws in excel_results['worksheets']]) / worksheets_count
        avg_phone_quality = sum([ws['data_quality'].get('phone_quality', 0) for ws in excel_results['worksheets']]) / worksheets_count
        
        print(f"平均数据完整度: {avg_completeness:.1f}%")
        print(f"平均邮箱质量: {avg_email_quality:.1f}%")
        print(f"平均手机号码质量: {avg_phone_quality:.1f}%")
        
        # 判断验证是否通过
        validation_passed = (
            avg_completeness >= 95 and
            avg_email_quality >= 95 and
            comparison_results and comparison_results['consistent']
        )
        
        print(f"\n验证结果: {'✓ 通过' if validation_passed else '✗ 未通过'}")
        
        if validation_passed:
            print("文档符合保险公司备案要求，可以提交使用。")
        else:
            print("文档存在质量问题，建议检查后重新生成。")
        
        return validation_passed

def main():
    """
    @function main - 主函数
    @returns {None}
    """
    validator = DocumentValidator()
    validator.validate_document()

if __name__ == "__main__":
    main()
